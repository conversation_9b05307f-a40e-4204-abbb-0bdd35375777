
#include "register/tilingdata_base.h"

namespace optiling {
BEGIN_TILING_DATA_DEF(LcmTilingData)
  // 核心计算参数
  TILING_DATA_FIELD_DEF(uint32_t, smallCoreDataNum); // 小核数据量大小    
  TILING_DATA_FIELD_DEF(uint32_t, bigCoreDataNum);   // 大核数据量大小 
  TILING_DATA_FIELD_DEF(uint32_t, finalbigCoreTileNum);   // 大核数据搬运次数       
  TILING_DATA_FIELD_DEF(uint32_t, finalsmallCoreTileNum);   // 小核数据搬运次数  
  TILING_DATA_FIELD_DEF(uint32_t, tileDataNum);       // 单核单次搬运处理数据量大小  
  TILING_DATA_FIELD_DEF(uint32_t, smallTailDataNum);  // 小核尾部数据量大小
  TILING_DATA_FIELD_DEF(uint32_t, bigTailDataNum);    // 大核尾部数据量大小       
  TILING_DATA_FIELD_DEF(uint32_t, TailBlockNum);      // 大核的个数     
END_TILING_DATA_DEF;

// 注册 Lcm 算子的 tiling 数据结构，便于框架自动识别和调用
REGISTER_TILING_DATA_CLASS(Lcm, LcmTilingData)
}
