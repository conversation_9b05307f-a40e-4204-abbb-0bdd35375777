{"op": "Lcm", "language": "cpp", "input_desc": [{"name": "input", "param_type": "required", "type": ["int8", "int16", "int32", "int64"], "format": ["ND", "ND", "ND", "ND"]}, {"name": "other", "param_type": "required", "type": ["int8", "int16", "int32", "int64"], "format": ["ND", "ND", "ND", "ND"]}], "output_desc": {"name": "out", "param_type": "required", "type": ["int8", "int16", "int32", "int64"], "format": ["ND", "ND", "ND", "ND"]}}