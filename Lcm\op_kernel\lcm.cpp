#include "kernel_operator.h"
using namespace AscendC;
constexpr int32_t BUFFER_NUM = 2;

template<typename TYPE_INPUT, typename TYPE_OTHER, typename TYPE_OUT>
  class KernelLcm {
    using T = TYPE_INPUT;
public:
    __aicore__ inline KernelLcm() {}
    __aicore__ inline void Init(GM_ADDR input, GM_ADDR other, GM_ADDR out, uint32_t smallCoreDataNum, 
        uint32_t bigCoreDataNum, uint32_t finalBigCoreTileNum,
        uint32_t finalSmallCoreTileNum, uint32_t tileDataNum,
        uint32_t smallTailDataNum, uint32_t bigTailDataNum,
        uint32_t TailBlockNum)
    {
        //考生补充初始化代码
        ASSERT(GetBlockNum() != 0 && "block dim can not be zero!");
        uint32_t coreNum = AscendC::GetBlockIdx();
        uint32_t globalBufferIndex = bigCoreDataNum * AscendC::GetBlockIdx();
        this->tileDataNum = tileDataNum;

        if(coreNum < TailBlockNum) {
            this->coreDataNum = bigCoreDataNum;
            this->tileNum = finalBigCoreTileNum;
            this->tailDataNum = bigTailDataNum;
        } else {
            this->coreDataNum = smallCoreDataNum;
            this->tileNum = finalSmallCoreTileNum;
            this->tailDataNum = smallTailDataNum;
            globalBufferIndex = (bigCoreDataNum - smallCoreDataNum) * AscendC::GetBlockIdx() + tailDataNum;
        }

        inputGm.SetGlobalBuffer((__gm__ T*)input + globalBufferIndex, this->coreDataNum);
        otherGm.SetGlobalBuffer((__gm__ T*)other + globalBufferIndex, this->coreDataNum);
        outGm.SetGlobalBuffer((__gm__ T*)out + globalBufferIndex, this->coreDataNum);

        pipe.InitBuffer(inQueueinput, BUFFER_NUM, this->tileLength * sizeof(T));
        pipe.InitBuffer(inQueueother, BUFFER_NUM, this->tileLength * sizeof(T));
        pipe.InitBuffer(outQueueout, BUFFER_NUM, this->tileLength * sizeof(T));
        // pipe.InitBuffer(tmpBuffer1, this->tileLength * sizeof(T));
        // pipe.InitBuffer(tmpBuffer2, this->tileLength * sizeof(T));
        // pipe.InitBuffer(tmpBuffer3, this->tileLength * sizeof(T));
        // pipe.InitBuffer(tmpBuffer4, this->tileLength * sizeof(T));
    }
    __aicore__ inline void Process()
    {
        int32_t loopCount = this->tileNum;
        this->processDataNum = this->tileDataNum;
        for (int32_t i = 0; i < loopCount; i++) {
            if(i == this->tileNum - 1) {
                this->processDataNum = this->tailDataNum;
            }
            CopyIn(i);
            Compute(i);
            CopyOut(i);
        }
    }
 
private:
    __aicore__ inline void CopyIn(int32_t progress)
    {
        LocalTensor<T> inputLocal = inQueueinput.AllocTensor<T>();
        LocalTensor<T> otherLocal = inQueueother.AllocTensor<T>();
        LocalTensor<T> outLocal = outQueueout.AllocTensor<T>();
        DataCopy(inputLocal, inputGm[progress * this->tileDataNum], this->processDataNum);
        DataCopy(otherLocal, otherGm[progress * this->tileDataNum], this->processDataNum);
        DataCopy(outLocal, outGm[progress * this->tileDataNum], this->processDataNum);
        inQueueinput.EnQue(inputLocal);
        inQueueother.EnQue(otherLocal);
        outQueueout.EnQue(outLocal);
    }
    __aicore__ inline void Compute(int32_t progress)
    {
        // 从输入队列中取出当前块的数据
        LocalTensor<T> inputLocal = inQueueinput.DeQue<T>();
        LocalTensor<T> otherLocal = inQueueother.DeQue<T>();

        // 从输出队列分配空间用于存储结果
        LocalTensor<T> outLocal = outQueueout.AllocTensor<T>();

        // 遍历当前块的数据，逐元素计算 LCM
        for (int32_t i = 0; i < this->tileLength; i++) {
            outLocal[i] = compute_lcm(inputLocal[i], otherLocal[i]);
        }

        // 将结果放入输出队列
        outQueueout.EnQue(outLocal);

        // 释放输入数据占用的空间
        inQueueinput.FreeTensor(inputLocal);
        inQueueother.FreeTensor(otherLocal);
    }
    __aicore__ inline void CopyOut(int32_t progress)
    {
        // 考生补充算子代码
        // 从队列取出计算结果
        LocalTensor<T> outLocal = outQueueout.DeQue<T>();
        
        // 复制到全局内存
        DataCopy(outGm[progress * this->tileLength], 
                outLocal, 
                this->tileLength);
        
        // 释放本地缓存
        outQueueout.FreeTensor(outLocal);
    }
 
private:
    TPipe pipe;
    TQue<QuePosition::VECIN, BUFFER_NUM> inQueueinput, inQueueother;
    TQue<QuePosition::VECOUT, BUFFER_NUM> outQueueout;
    GlobalTensor<T> inputGm, otherGm, outGm;

    uint32_t coreDataNum;
    uint32_t tileNum;
    uint32_t tileDataNum;
    uint32_t processDataNum;
    uint32_t tailDataNum;
};

// 模板函数：计算两个数的最大公约数（GCD）
template <typename T>
__inline__ __aicore__ T gcd(T a, T b) {
    while (b != 0) {
        T temp = b;
        b = a % b;
        a = temp;
    }
    return a;
}

// 模板函数：计算两个数的最小公倍数（LCM）
template <typename T>
__inline__ __aicore__ T compute_lcm(T a, T b) {
    if (a == 0 || b == 0) return 0;
    return (a / gcd(a, b)) * b;
}

// 核心算子实现
extern "C" __global__ __aicore__ void lcm(GM_ADDR input, GM_ADDR other, GM_ADDR out, GM_ADDR workspace, GM_ADDR tiling) {
    // 获取 tiling 数据
    GET_TILING_DATA(tiling_data, tiling);

    KernelLcm<TYPE_INPUT, TYPE_OTHER, TYPE_OUT> op;
    
    // 初始化并执行

    op.Init(input, other, out, tiling_data.smallCoreDataNum, 
        tiling_data.bigCoreDataNum, tiling_data.finalBigCoreTileNum,
        tiling_data.finalSmallCoreTileNum, tiling_data.tileDataNum,
        tiling_data.smallTailDataNum, tiling_data.bigTailDataNum,
        tiling_data.TailBlockNum);
    op.Process();
}